package com.haoxue.sportai.test

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch

/**
 * 测试 ViewModel 依赖是否正确导入
 * 如果这个文件没有编译错误，说明依赖添加成功
 */
class ViewModelTest : ViewModel() {
    
    fun testViewModelScope() {
        viewModelScope.launch {
            // 测试 viewModelScope 是否可用
            println("ViewModelScope 工作正常")
        }
    }
}
