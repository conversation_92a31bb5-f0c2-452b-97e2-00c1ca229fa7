package com.haoxue.sportai.viewmodel

import androidx.lifecycle.viewModelScope
import com.haoxue.libcommon.markdown.BasicChatAdapter
import com.haoxue.libcommon.ui.viewmodel.BaseViewModel
import com.haoxue.sportai.repository.McpRepository
import com.haoxue.sportai.model.McpUiState
import com.haoxue.sportai.model.SttState
import com.haoxue.sportai.model.AudioState
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import com.lazy.library.logging.Logcat
import kotlin.coroutines.cancellation.CancellationException

/**
 * MCP Activity 的 ViewModel
 * 负责管理所有业务逻辑和状态
 */
class McpViewModel(
    private val repository: McpRepository
) : BaseViewModel() {

    // UI 状态
    private val _uiState = MutableStateFlow(McpUiState())
    val uiState: StateFlow<McpUiState> = _uiState.asStateFlow()

    // STT 状态
    private val _sttState = MutableStateFlow(SttState())
    val sttState: StateFlow<SttState> = _sttState.asStateFlow()

    // 音频状态
    private val _audioState = MutableStateFlow(AudioState())
    val audioState: StateFlow<AudioState> = _audioState.asStateFlow()

    // 聊天消息列表
    private val _chatMessages = MutableStateFlow<List<BasicChatAdapter.ChatMessage>>(emptyList())
    val chatMessages: StateFlow<List<BasicChatAdapter.ChatMessage>> = _chatMessages.asStateFlow()

    // STT 防抖任务
    private var sttDebounceJob: Job? = null
    private val sttDebounceDelayMs = 1500L

    // 当前用户消息索引
    private var currentUserMessageIndex = -1

    init {
        // 初始化仓库监听
        observeRepositoryStates()
    }

    /**
     * 监听仓库状态变化
     */
    private fun observeRepositoryStates() {
        viewModelScope.launch {
            // 监听音频播放状态
            repository.audioPlaybackState.collect { state ->
                _audioState.value = _audioState.value.copy(
                    isPlaying = state.isPlaying,
                    queueSize = state.queueSize
                )
            }
        }

        viewModelScope.launch {
            // 监听WebSocket连接状态
            repository.webSocketState.collect { state ->
                _uiState.value = _uiState.value.copy(
                    isWebSocketConnected = state.isConnected,
                    reconnectInfo = state.reconnectInfo
                )
            }
        }
    }

    /**
     * 开始/停止录音
     */
    fun toggleRecording() {
        val currentState = _sttState.value
        if (currentState.isRecording) {
            stopRecording()
        } else {
            startRecording()
        }
    }

    /**
     * 开始录音
     */
    private fun startRecording() {
        launchSafely(showLoading = false) {
            repository.startRecording { recognizedText ->
                handleSttText(recognizedText)
            }
            _sttState.value = _sttState.value.copy(isRecording = true)
        }
    }

    /**
     * 停止录音
     */
    private fun stopRecording() {
        launchSafely(showLoading = false) {
            repository.stopRecording()
            _sttState.value = _sttState.value.copy(isRecording = false)
        }
    }

    /**
     * 处理STT文本（防抖机制）
     */
    private fun handleSttText(text: String) {
        if (text.isBlank()) return

        // 如果识别到新文本，停止音频播放
        if (text.isNotBlank()) {
            repository.stopAudioPlayback()
        }

        // 取消之前的防抖任务
        sttDebounceJob?.cancel()

        // 拼接文本
        val currentSttState = _sttState.value
        val newText = if (currentSttState.currentText.isEmpty()) {
            text
        } else {
            "${currentSttState.currentText} $text"
        }

        _sttState.value = currentSttState.copy(currentText = newText)

        // 立即显示并检查本地指令
        val localResult = displayCurrentSttText(newText)
        if (localResult != null) {
            // 匹配到本地指令，立即执行
            handleLocalCommand(localResult)
            return
        }

        // 启动防抖任务
        sttDebounceJob = viewModelScope.launch {
            try {
                delay(sttDebounceDelayMs)
                // 防抖完成，发送AI请求
                finalizeUserMessageForAI(newText)
                clearSttState()
            } catch (e: CancellationException) {
                Logcat.d("STT 防抖任务被取消")
            }
        }
    }

    /**
     * 显示当前STT文本并检查本地指令
     */
    private fun displayCurrentSttText(text: String): String? {
        if (currentUserMessageIndex == -1) {
            // 创建新的用户消息
            addUserMessage(text)
        } else {
            // 更新现有消息
            updateUserMessage(currentUserMessageIndex, text)
        }

        // 检查本地指令
        return repository.checkLocalCommand(text)
    }

    /**
     * 处理本地指令
     */
    private fun handleLocalCommand(result: String) {
        addAiMessage(result, isMarkdown = false)
        clearSttState()
        Logcat.d("本地指令执行: $result")
    }

    /**
     * 完成用户消息并请求AI回复
     */
    private fun finalizeUserMessageForAI(text: String) {
        if (currentUserMessageIndex == -1) {
            addUserMessage(text)
        } else {
            updateUserMessage(currentUserMessageIndex, text)
        }

        // 请求AI回复
        requestAiResponse(text)
    }

    /**
     * 请求AI回复
     */
    private fun requestAiResponse(question: String) {
        launchSafely(showLoading = false) {
            // 添加AI消息占位符
            val aiMessageIndex = addAiMessage("", isMarkdown = false)

            repository.sendSseRequest(
                question = question,
                onMessage = { content ->
                    updateAiMessage(aiMessageIndex, content)
                },
                onComplete = { completeResponse ->
                    updateAiMessage(aiMessageIndex, completeResponse, isComplete = true)
                },
                onError = { error ->
                    updateAiMessage(aiMessageIndex, "❌ 请求失败: $error", isComplete = true)
                }
            )
        }
    }

    /**
     * 添加用户消息
     */
    private fun addUserMessage(content: String): Int {
        val message = BasicChatAdapter.ChatMessage(
            type = 0, // 用户消息
            content = content,
            isMarkdown = false
        )
        val currentMessages = _chatMessages.value.toMutableList()
        currentMessages.add(message)
        _chatMessages.value = currentMessages
        
        currentUserMessageIndex = currentMessages.size - 1
        return currentUserMessageIndex
    }

    /**
     * 添加AI消息
     */
    private fun addAiMessage(content: String, isMarkdown: Boolean = false): Int {
        val message = BasicChatAdapter.ChatMessage(
            type = 1, // AI消息
            content = content,
            isMarkdown = isMarkdown
        )
        val currentMessages = _chatMessages.value.toMutableList()
        currentMessages.add(message)
        _chatMessages.value = currentMessages
        
        return currentMessages.size - 1
    }

    /**
     * 更新用户消息
     */
    private fun updateUserMessage(index: Int, content: String) {
        val currentMessages = _chatMessages.value.toMutableList()
        if (index < currentMessages.size) {
            currentMessages[index] = currentMessages[index].copy(content = content)
            _chatMessages.value = currentMessages
        }
    }

    /**
     * 更新AI消息
     */
    private fun updateAiMessage(index: Int, content: String, isComplete: Boolean = false) {
        val currentMessages = _chatMessages.value.toMutableList()
        if (index < currentMessages.size) {
            val isMarkdown = if (isComplete) {
                repository.isMarkdownContent(content)
            } else {
                currentMessages[index].isMarkdown
            }
            
            currentMessages[index] = currentMessages[index].copy(
                content = content,
                isMarkdown = isMarkdown
            )
            _chatMessages.value = currentMessages
        }
    }

    /**
     * 清除STT状态
     */
    private fun clearSttState() {
        _sttState.value = SttState()
        currentUserMessageIndex = -1
    }

    /**
     * 手动重连WebSocket
     */
    fun manualReconnectWebSocket() {
        launchSafely(showLoading = false) {
            repository.manualReconnectWebSocket()
        }
    }

    /**
     * 滚动到底部
     */
    fun scrollToBottom() {
        _uiState.value = _uiState.value.copy(shouldScrollToBottom = true)
    }

    /**
     * 重置滚动状态
     */
    fun resetScrollState() {
        _uiState.value = _uiState.value.copy(shouldScrollToBottom = false)
    }

    /**
     * 更新滚动按钮可见性
     */
    fun updateScrollButtonVisibility(shouldShow: Boolean) {
        _uiState.value = _uiState.value.copy(showScrollButton = shouldShow)
    }

    /**
     * 初始化Repository
     */
    fun initializeRepository() {
        repository.initialize()
    }

    override fun onCleared() {
        super.onCleared()
        sttDebounceJob?.cancel()
        repository.cleanup()
    }
}
