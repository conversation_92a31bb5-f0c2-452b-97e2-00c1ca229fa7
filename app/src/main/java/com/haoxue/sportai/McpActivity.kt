package com.haoxue.sportai

import androidx.lifecycle.lifecycleScope
import com.haoxue.libcommon.ConstData
import com.haoxue.libcommon.markdown.BasicMarkdownRenderer
import com.haoxue.libcommon.markdown.BasicMarkdownUtils
import com.haoxue.libcommon.markdown.BasicChatAdapter
import com.haoxue.libcommon.markdown.BasicMarkdownRecyclerUtils
import com.haoxue.libcommon.singleClick
import com.haoxue.libcommon.ui.activity.BaseActivity
import com.haoxue.libcommon.utils.AudioPlaybackUtils
import com.haoxue.libcommon.utils.SafeWebSocketUtils
import com.haoxue.libcommon.utils.SseUtils
import com.haoxue.mcpserver.McpServerHelper
import com.haoxue.mcpserver.ToolsUtils
import com.haoxue.sportai.databinding.ActivityMcpBinding
import com.haoxue.stt.SttHelper
import com.lazy.library.logging.Logcat
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import kotlin.coroutines.cancellation.CancellationException

class McpActivity : BaseActivity<ActivityMcpBinding>(R.layout.activity_mcp) {
    // 使用基础的聊天适配器
    private lateinit var chatAdapter: BasicChatAdapter

    // Markdown 相关
    private val markdownBuilder = StringBuilder()
    private var isStreamingMarkdown = false

    // STT 防抖和文本拼接相关
    private var sttDebounceJob: Job? = null
    private val sttTextBuilder = StringBuilder()
    private var lastSttText = ""
    private val sttDebounceDelayMs = 2000L // 防抖延迟

    // 当前用户消息的索引（用于实时更新）
    private var currentUserMessageIndex = -1

    override fun initCommonData() {
        mBinding.add.singleClick {
            SttHelper.startRecord(this, { isRecording ->
                mBinding.add.text = if (isRecording) "结束" else "开始"
            }) { recognizedText ->
                Logcat.d("stt---${recognizedText}")
                // 使用防抖机制处理STT文本
                handleSttTextWithDebounce(recognizedText)
            }
        }
        mBinding.scrollToBottomFab.singleClick {
            BasicMarkdownRecyclerUtils.forceScrollToBottom(mBinding.recyclerView)
        }

        mBinding.scrollToBottomFab.setOnLongClickListener {
            SafeWebSocketUtils.manualReconnect()
            Logcat.d("手动触发WebSocket重连")
            true
        }
    }

    /**
     * STT 文本处理 - 立即显示，延迟请求
     * @param text 识别到的文本
     * @param forceExecute 是否强制执行（用于录音结束时）
     */
    private fun handleSttTextWithDebounce(text: String) {
        // 如果识别到新的文本片段，立即打断当前 TTS 播放并清空队列，准备后续新语音
        if (text.isNotBlank()) {
            runOnUiThread {
                AudioPlaybackUtils.stop()
            }
        }

        // 取消之前的防抖任务
        sttDebounceJob?.cancel()

        if (text.isBlank()) return

        // 直接拼接文本（不做重复判断）
        appendSttText(text)

        // 立即显示当前拼接的文本
        runOnUiThread {
            var displayCurrentSttText = displayCurrentSttText()
            if (displayCurrentSttText!==null){
                // 清空文本构建器，准备下一轮
                sttTextBuilder.clear()
                lastSttText = ""
                currentUserMessageIndex = -1
                return@runOnUiThread
            }
        }

        Logcat.d("STT 实时: 收到文本 '$text'，当前拼接: '${sttTextBuilder.toString().trim()}'")
        lastSttText = text

        // 重启防抖任务（每次新文本都重新计时）
        sttDebounceJob = lifecycleScope.launch {
            try {
                delay(sttDebounceDelayMs)

                // 防抖时间到，处理拼接的完整文本
                val completeText = sttTextBuilder.toString().trim()
                if (completeText.isNotBlank()) {
                    Logcat.d("STT 防抖完成: 处理完整文本 '$completeText'")

                    // 最终更新用户消息并请求AI回复
                    finalizeUserMessage(completeText)

                    // 清空文本构建器，准备下一轮
                    sttTextBuilder.clear()
                    lastSttText = ""
                    currentUserMessageIndex = -1
                } else {
                    Logcat.d("STT 防抖完成: 文本为空，跳过处理")
                }
            } catch (e: CancellationException) {
                Logcat.d("STT 防抖: 任务被取消（收到新文本）")
            } catch (e: Exception) {
                Logcat.e("STT 防抖处理异常", e)
            }
        }
    }

    /**
     * 立即显示当前STT文本
     */
    private fun displayCurrentSttText(): String? {
        val currentText = sttTextBuilder.toString().trim()
        if (currentText.isBlank()) return null

        if (currentUserMessageIndex == -1) {
            // 第一次显示，创建新的用户消息
            val userMessage = BasicChatAdapter.ChatMessage(
                type = 0, // 用户消息
                content = currentText,
                isMarkdown = false
            )
            chatAdapter.addMessage(userMessage)
            currentUserMessageIndex = chatAdapter.itemCount - 1

            Logcat.d("STT 实时显示: 创建新消息 '$currentText'")
        } else {
            // 更新现有的用户消息
            chatAdapter.updateMessage(currentUserMessageIndex, currentText)

            Logcat.d("STT 实时更新: 更新消息 '$currentText'")
        }

        // 滚动到底部显示最新内容
        BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)

        // 检查本地指令拦截
        val result = ToolsUtils.interceptFun(currentText)
        if (result != null) {
            // 如果匹配本地指令，直接返回结果，不调用AI
            val aiMessage = BasicChatAdapter.ChatMessage(
                type = 1, // AI消息
                content = result,
                isMarkdown = false
            )
            chatAdapter.addMessage(aiMessage)
            BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)
        }
        return result
    }

    /**
     * 完成用户消息并请求AI回复
     */
    private fun finalizeUserMessage(completeText: String) {
        Logcat.d("STT 完成: 最终文本 '$completeText'")

        // 确保用户消息已显示
        if (currentUserMessageIndex == -1) {
            val userMessage = BasicChatAdapter.ChatMessage(
                type = 0, // 用户消息
                content = completeText,
                isMarkdown = false
            )
            chatAdapter.addMessage(userMessage)
        } else {
            // 最终更新用户消息
            chatAdapter.updateMessage(currentUserMessageIndex, completeText)
        }
        // 请求AI回复
        callSseExample(question = completeText)
    }

    /**
     * 直接拼接 STT 文本
     */
    private fun appendSttText(newText: String) {
        if (sttTextBuilder.isNotEmpty()) {
            // 如果已有文本，添加空格分隔
            sttTextBuilder.append(" ")
        }
        sttTextBuilder.append(newText)
        Logcat.d("STT 拼接: 添加文本 '$newText'，当前完整文本: '${sttTextBuilder.toString()}'")
    }





    /**
     * SSE 调用示例 - 使用优化的 Markwon RecyclerView 适配器
     */
    private fun callSseExample(question: String = "") {
        // 重置 Markdown 构建器
        markdownBuilder.clear()
        isStreamingMarkdown = true

        // 添加 AI 回复的占位符
        val aiMessage = BasicChatAdapter.ChatMessage(
            type = 1, // AI 消息
            content = "",
            isMarkdown = false
        )
        chatAdapter.addMessage(aiMessage)

        SseUtils.quickAsk(
            lifecycleScope,
            question,
            onComplete = { completeResponse ->
                runOnUiThread {
                    Logcat.d("SSE 完整回答: $completeResponse")
                    isStreamingMarkdown = false

                    // 最终更新完整的 Markdown 内容
                    val isMarkdown = BasicMarkdownUtils.isMarkdown(completeResponse)
                    chatAdapter.updateLastMessage(completeResponse, isMarkdown)

                    // 自动滚动到底部
                    BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)
                }
            },
            onError = { error ->
                runOnUiThread {
                    isStreamingMarkdown = false
                    chatAdapter.updateLastMessage("❌ 请求失败: $error", false)
                    Logcat.e("SSE 请求失败: $error")
                }
            },
            onMessage = { content ->
                runOnUiThread {
                    // 流式更新 Markdown 内容
                    markdownBuilder.append(content)

                    val currentContent = markdownBuilder.toString()
                    val isMarkdown = BasicMarkdownUtils.isMarkdown(currentContent)

                    // 更新最后一条消息
                    chatAdapter.updateLastMessage(currentContent, isMarkdown)

                    // 自动滚动到底部
                    BasicMarkdownRecyclerUtils.scrollToBottom(mBinding.recyclerView)
                }
            }
        )
    }



    override fun initCommonListener() {
        SttHelper.init(this)
        McpServerHelper.init(this)
        AudioPlaybackUtils.initialize(this)

        // 初始化基础版 Markdown 渲染器
        BasicMarkdownRenderer.initialize(this)

        // 配置WebSocket自动重连
        SafeWebSocketUtils.configureReconnect(
            enabled = true,
            maxAttempts = -1,      // 无限重连，直到连接成功
            intervalMs = 2000L,    // 基础间隔2秒
            maxIntervalMs = 10000L // 最大间隔30秒
        )

        // 设置基础聊天适配器
        chatAdapter = BasicMarkdownRecyclerUtils.setupChatRecyclerView(
            mBinding.recyclerView,
            this,
            onItemClick = { message, position ->
                // 点击消息的处理
                Logcat.d("点击消息: ${message.content}")
            },
            onScrollStateChanged = { shouldShowScrollButton ->
                // 控制滚动到底部按钮的显示/隐藏
                runOnUiThread {
                    mBinding.scrollToBottomFab.visibility = if (shouldShowScrollButton) {
                        android.view.View.VISIBLE
                    } else {
                        android.view.View.GONE
                    }
                }
            }
        )
        SafeWebSocketUtils.safeConnect(
            lifecycleScope,
            ConstData.TTS_WEBSOCKET_URL,
            onMessage = { message ->
                Logcat.d("WebSocket 收到音频消息: $message")
                AudioPlaybackUtils.playAudio(message)
            },
            onConnected = {
                Logcat.d("🔗 WebSocket 音频连接成功")
                Logcat.d("📊 ${SafeWebSocketUtils.getReconnectInfo()}")
            },
            onDisconnected = {
                Logcat.d("❌ WebSocket 音频连接断开")
                // 连接断开时会自动尝试重连（如果启用）
            },
            onError = { error ->
                Logcat.e("🚨 WebSocket 音频连接错误: $error")
                // 错误时也会自动尝试重连（如果启用）
            }
        )

        // 设置音频播放队列变化监听器
        AudioPlaybackUtils.setOnQueueChangedListener { queueSize ->
            Logcat.d("音频队列长度变化: $queueSize")
        }

        // 设置音频播放状态监听器
        AudioPlaybackUtils.setOnPlaybackStateChangedListener { state ->
            Logcat.d("音频播放状态变化: $state")
        }

        // 设置音频播放错误监听器
        AudioPlaybackUtils.setOnPlaybackErrorListener { error, exception ->
            Logcat.e("音频播放错误: $error", exception)
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 取消 STT 防抖任务并清空文本构建器
        sttDebounceJob?.cancel()
        sttTextBuilder.clear()

        // 清理 RecyclerView 滚动监听器
        BasicMarkdownRecyclerUtils.cleanup(mBinding.recyclerView)

        SttHelper.release()
        McpServerHelper.stopServer()
        AudioPlaybackUtils.release()
    }

    override fun requestCommonData() {
    }
}